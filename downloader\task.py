from app import celery
import re
import time
import os
import requests
import hashlib
from utils import download_audio, transcode_audio, verify_jwt, send_callback, upload_to_webdav
from config import DOWNLOAD_KEY, TRANSCODED_DIR, DOWNLOAD_DIR, BACKEND_URL, OUTPUT_DIR

@celery.task(bind=True,queue=DOWNLOAD_QUEUE)
def download_file(self, song_hash, song_title, url):
    """下载文件的 Celery 任务"""
    task_id = f"download_{song_hash}"
    self.request.id = task_id  # 设定 Celery 任务 ID
    try:
        download_audio(song_hash=song_hash, song_title=song_title, song_url=url)
        file_path = os.path.join(OUTPUT_DIR, f"{song_hash}_{song_title}.webm")

        for _ in range(8):
            if os.path.exists(file_path):
                DATA = {
                    "secret": DOWNLOAD_KEY,  # 替换为正确的密钥
                    "hash": song_hash  # 替换为歌曲的哈希值
                }
                requests.post(BACKEND_URL + 'update_download_status/', json=DATA)
                print(f"处理完成，文件保存在: {file_path}")
                return
            time.sleep(1)

        print("超时: 文件未在规定时间内出现")
    except Exception as exc:
        print(f"Download failed: {str(exc)}")
        raise exc

@celery.task(bind=True,queue=TRANSCODE_QUEUE)
def transcode_file(self, input_path, output_path, format, image):
    """转码文件的 Celery 任务"""
    task_id = output_path
    self.request.id = task_id  # 设定 Celery 任务 ID
    try:
        transcode_audio(input_path, output_path, format, image)
        print(f"Successfully transcoded {input_path}")
    except Exception as exc:
        print(f"Transcoding failed: {str(exc)}")
        raise exc

@celery.task(bind=True,queue=UPLOAD_QUEUE)
def webdav_upload_task(self, song_hash,song_artist ,song_title, webdav_url, username, password, format, task_id):
    """上传至 WebDAV 的 Celery 任务"""
    song_hashname = hashlib.md5((song_hash + DOWNLOAD_KEY).encode()).hexdigest()
    download_path = os.path.join(DOWNLOAD_DIR, f"{song_hash}_{song_hashname}.webm")
    image_path = os.path.join(DOWNLOAD_DIR, f"{song_hash}_{song_hashname}.webp")
    transcode_path = os.path.join(TRANSCODED_DIR, f"{song_hash}_{song_hashname}.{format}")

    attempts = 0
    while attempts < 30:
        if os.path.exists(download_path):
            print(f"Found downloaded file {download_path}, adding transcode task...")
            if os.path.exists(transcode_path):
                break
            else:
                transcode_file.apply_async((download_path, transcode_path, format, image_path), task_id=transcode_path)
                break
        time.sleep(2)
        attempts += 1

    attempts = 0
    while attempts < 30:
        if os.path.exists(transcode_path):
            print(f"Found transcoded file {transcode_path}, starting upload...")
            upload_to_webdav(transcode_path, webdav_url, username, password, task_id, song_artist,song_title, format)
            return
        time.sleep(2)
        attempts += 1

    print(f"WebDAV upload task for {song_title} timed out.")
    send_callback(task_id, False, 'timeout')




