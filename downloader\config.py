import datetime
import os

DOWNLOAD_KEY = '1p5kh&FPTA9W2VLF=n6~&J]u1cx'
SECRET_KEY = 'django-insecure-enh3gcir$=4qr$wt)lk4rnw57_y8%hk&1apc9#)m6=p+_p5j)s'

# 基础目录配置
BASE_DOWNLOAD_DIR = "./Download"
TEMP_DIR = "./Temp"
COOKIES_FILE = "cookies.txt"
BACKEND_URL = "https://1music.cc/backend/"

# 分区配置
DEFAULT_PARTITION = 0
MAX_PARTITIONS = 10

# 动态生成分区目录
def get_partition_dir(partition=DEFAULT_PARTITION):
    """获取指定分区的目录路径"""
    return os.path.join(BASE_DOWNLOAD_DIR, str(partition))

# 兼容性配置（保持向后兼容）
OUTPUT_DIR = get_partition_dir(DEFAULT_PARTITION)
DOWNLOAD_DIR = get_partition_dir(DEFAULT_PARTITION)

# JWT配置（已废弃，保留用于兼容性）
JWT_CONFIG = {
    'ALGORITHM': 'HS256',
    'EXPIRATION_DELTA': datetime.timedelta(minutes=30),
}