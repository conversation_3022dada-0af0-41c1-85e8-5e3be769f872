globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/[locale]/page"] = {"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/src/app/[locale]/page.js":{"id":"[project]/src/app/[locale]/page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_f58f64._.js","static/chunks/src_app_[locale]_layout_b5d566.js","static/chunks/src_app_[locale]_layout_61af54.js","static/chunks/node_modules_@mui_system_esm_e0a825._.js","static/chunks/node_modules_@mui_material_89cd74._.js","static/chunks/node_modules_axios_lib_c4c49c._.js","static/chunks/node_modules_88b03c._.js","static/chunks/src_app_[locale]_df39e1._.js","static/chunks/src_app_[locale]_page_360e66.js"],"async":false},"[project]/node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js":{"id":"[project]/node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_f58f64._.js","static/chunks/src_app_[locale]_layout_b5d566.js","static/chunks/src_app_[locale]_layout_61af54.js"],"async":false},"[project]/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js <module evaluation>":{"id":"[project]/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_f58f64._.js","static/chunks/src_app_[locale]_layout_b5d566.js","static/chunks/src_app_[locale]_layout_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js <module evaluation>":{"id":"[project]/node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_f58f64._.js","static/chunks/src_app_[locale]_layout_b5d566.js","static/chunks/src_app_[locale]_layout_61af54.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"async":false},"[project]/src/app/[locale]/page.js <module evaluation>":{"id":"[project]/src/app/[locale]/page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_f58f64._.js","static/chunks/src_app_[locale]_layout_b5d566.js","static/chunks/src_app_[locale]_layout_61af54.js","static/chunks/node_modules_@mui_system_esm_e0a825._.js","static/chunks/node_modules_@mui_material_89cd74._.js","static/chunks/node_modules_axios_lib_c4c49c._.js","static/chunks/node_modules_88b03c._.js","static/chunks/src_app_[locale]_df39e1._.js","static/chunks/src_app_[locale]_page_360e66.js"],"async":false},"[project]/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":{"id":"[project]/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_f58f64._.js","static/chunks/src_app_[locale]_layout_b5d566.js","static/chunks/src_app_[locale]_layout_61af54.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_87c5e4._.js","server/chunks/ssr/[root of the server]__29912d._.js"],"async":false}},"[project]/src/app/[locale]/page.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/[locale]/page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_257b04._.js","server/chunks/ssr/[root of the server]__26182f._.js","server/chunks/ssr/node_modules_@mui_system_esm_679315._.js","server/chunks/ssr/node_modules_@mui_material_c7825f._.js","server/chunks/ssr/node_modules_axios_lib_04a463._.js","server/chunks/ssr/node_modules_mime-db_600f3c._.js","server/chunks/ssr/node_modules_311a26._.js","server/chunks/ssr/[root of the server]__186597._.js"],"async":false}},"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_87c5e4._.js","server/chunks/ssr/[root of the server]__29912d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_87c5e4._.js","server/chunks/ssr/[root of the server]__29912d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_87c5e4._.js","server/chunks/ssr/[root of the server]__29912d._.js"],"async":false}},"[project]/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_257b04._.js","server/chunks/ssr/[root of the server]__26182f._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_87c5e4._.js","server/chunks/ssr/[root of the server]__29912d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_87c5e4._.js","server/chunks/ssr/[root of the server]__29912d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_87c5e4._.js","server/chunks/ssr/[root of the server]__29912d._.js"],"async":false}},"[project]/node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_257b04._.js","server/chunks/ssr/[root of the server]__26182f._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/src/app/[locale]/page.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/[locale]/page.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}},"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}},"[project]/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}},"[project]/node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js (client proxy)","name":"*","chunks":["server/app/[locale]/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/[locale]/page":[],"[project]/src/app/[locale]/layout":[],"[project]/node_modules/next/dist/client/components/not-found-error":[]},"entryJSFiles":{"[project]/node_modules/next/dist/client/components/not-found-error":["static/chunks/node_modules_next_dist_5d579c._.js","static/chunks/node_modules_next_dist_client_components_not-found-error_61af54.js"],"[project]/src/app/[locale]/layout":["static/chunks/node_modules_f58f64._.js","static/chunks/src_app_[locale]_layout_b5d566.js","static/chunks/src_app_[locale]_layout_61af54.js"],"[project]/src/app/[locale]/page":["static/chunks/node_modules_f58f64._.js","static/chunks/src_app_[locale]_layout_b5d566.js","static/chunks/src_app_[locale]_layout_61af54.js","static/chunks/node_modules_@mui_system_esm_e0a825._.js","static/chunks/node_modules_@mui_material_89cd74._.js","static/chunks/node_modules_axios_lib_c4c49c._.js","static/chunks/node_modules_88b03c._.js","static/chunks/src_app_[locale]_df39e1._.js","static/chunks/src_app_[locale]_page_360e66.js"]}}
