/**
 * Standalone image processing utility using Canvas API
 * This replaces the need for ffmpeg for image operations
 */

export class ImageProcessor {
    /**
     * Crop image to square and convert to JPEG
     * @param {File|Blob} imageFile - Input image file
     * @param {number} size - Output size (default: 500)
     * @param {number} quality - JPEG quality (0-1, default: 0.9)
     * @returns {Promise<Blob>} - Processed JPEG blob
     */
    static async cropToSquareJPEG(imageFile, size = 500, quality = 0.9) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            const handleImageLoad = () => {
                try {
                    // Calculate square crop dimensions
                    const cropSize = Math.min(img.width, img.height);
                    const offsetX = (img.width - cropSize) / 2;
                    const offsetY = (img.height - cropSize) / 2;
                    
                    // Set canvas size
                    canvas.width = size;
                    canvas.height = size;
                    
                    // Fill with white background (handles transparency)
                    ctx.fillStyle = '#FFFFFF';
                    ctx.fillRect(0, 0, size, size);
                    
                    // Enable image smoothing for better quality
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';
                    
                    // Draw cropped and scaled image
                    ctx.drawImage(
                        img,
                        offsetX, offsetY, cropSize, cropSize,  // Source rectangle
                        0, 0, size, size                       // Destination rectangle
                    );
                    
                    // Convert to JPEG blob
                    canvas.toBlob((blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            reject(new Error('Failed to convert image to JPEG'));
                        }
                    }, 'image/jpeg', quality);
                    
                } catch (error) {
                    reject(error);
                }
            };
            
            img.onload = handleImageLoad;
            img.onerror = () => {
                reject(new Error('Failed to load image'));
            };
            
            // Create object URL and load image
            const url = URL.createObjectURL(imageFile);
            img.src = url;
            
            // Clean up URL after processing
            const cleanup = () => {
                URL.revokeObjectURL(url);
            };
            
            // Wrap resolve/reject to ensure cleanup
            const originalResolve = resolve;
            const originalReject = reject;
            
            resolve = (value) => {
                cleanup();
                originalResolve(value);
            };
            
            reject = (error) => {
                cleanup();
                originalReject(error);
            };
        });
    }

    /**
     * Resize image while maintaining aspect ratio
     * @param {File|Blob} imageFile - Input image file
     * @param {number} maxWidth - Maximum width
     * @param {number} maxHeight - Maximum height
     * @param {string} format - Output format ('image/jpeg' or 'image/png')
     * @param {number} quality - Quality for JPEG (0-1)
     * @returns {Promise<Blob>} - Resized image blob
     */
    static async resizeImage(imageFile, maxWidth, maxHeight, format = 'image/jpeg', quality = 0.9) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            img.onload = () => {
                try {
                    // Calculate new dimensions
                    let { width, height } = img;
                    
                    if (width > height) {
                        if (width > maxWidth) {
                            height = (height * maxWidth) / width;
                            width = maxWidth;
                        }
                    } else {
                        if (height > maxHeight) {
                            width = (width * maxHeight) / height;
                            height = maxHeight;
                        }
                    }
                    
                    // Set canvas size
                    canvas.width = width;
                    canvas.height = height;
                    
                    // Enable high-quality rendering
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';
                    
                    // Draw resized image
                    ctx.drawImage(img, 0, 0, width, height);
                    
                    // Convert to blob
                    canvas.toBlob((blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            reject(new Error('Failed to resize image'));
                        }
                    }, format, quality);
                    
                } catch (error) {
                    reject(error);
                }
            };
            
            img.onerror = () => {
                reject(new Error('Failed to load image'));
            };
            
            const url = URL.createObjectURL(imageFile);
            img.src = url;
            
            // Cleanup
            img.onload = (originalOnload => {
                return function() {
                    URL.revokeObjectURL(url);
                    originalOnload.call(this);
                };
            })(img.onload);
        });
    }

    /**
     * Convert image format
     * @param {File|Blob} imageFile - Input image file
     * @param {string} format - Output format ('image/jpeg', 'image/png', 'image/webp')
     * @param {number} quality - Quality for lossy formats (0-1)
     * @returns {Promise<Blob>} - Converted image blob
     */
    static async convertFormat(imageFile, format = 'image/jpeg', quality = 0.9) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            img.onload = () => {
                try {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    // Fill with white background for JPEG
                    if (format === 'image/jpeg') {
                        ctx.fillStyle = '#FFFFFF';
                        ctx.fillRect(0, 0, canvas.width, canvas.height);
                    }
                    
                    ctx.drawImage(img, 0, 0);
                    
                    canvas.toBlob((blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            reject(new Error('Failed to convert image format'));
                        }
                    }, format, quality);
                    
                } catch (error) {
                    reject(error);
                }
            };
            
            img.onerror = () => {
                reject(new Error('Failed to load image'));
            };
            
            const url = URL.createObjectURL(imageFile);
            img.src = url;
            
            // Cleanup
            img.onload = (originalOnload => {
                return function() {
                    URL.revokeObjectURL(url);
                    originalOnload.call(this);
                };
            })(img.onload);
        });
    }
}

export default ImageProcessor;
