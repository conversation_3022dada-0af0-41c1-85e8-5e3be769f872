import os
import hashlib
import subprocess
import time

from yt_dlp import YoutubeDL

from config import (
    TEMP_DIR, COOKIES_FILE, BACKEND_URL, DOWNLOAD_KEY,
    RCLONE_COMMAND, PARTITION_REMOTE_MAPPING,
    get_remote_path, get_temp_upload_dir, get_temp_download_dir
)

def generate_secure_hash_salt(partition, music_hash, format_type):
    """
    生成安全的hash_salt，用于链接验证
    参数: partition(分区), music_hash(音乐hash), format_type(格式)
    返回: 加盐后的hash值
    """
    combined_string = f"{partition}_{music_hash}_{format_type}_{DOWNLOAD_KEY}"
    return hashlib.md5(combined_string.encode('utf-8')).hexdigest()

def rclone_move_to_remote(local_file_path, remote_path, filename):
    """
    使用rclone move将文件上传到远端
    参数:
    - local_file_path: 本地文件路径
    - remote_path: 远端路径 (如: s3:bucket-name)
    - filename: 文件名
    返回: (success: bool, error_message: str)
    """
    try:
        remote_file_path = f"{remote_path}/{filename}"
        cmd = [RCLONE_COMMAND, "move", local_file_path, remote_file_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print(f"Successfully moved {local_file_path} to {remote_file_path}")
            return True, None
        else:
            error_msg = f"rclone move failed: {result.stderr}"
            print(error_msg)
            return False, error_msg

    except subprocess.TimeoutExpired:
        error_msg = "rclone move timeout"
        print(error_msg)
        return False, error_msg
    except Exception as e:
        error_msg = f"rclone move error: {str(e)}"
        print(error_msg)
        return False, error_msg

def rclone_copy_from_remote(remote_path, filename, local_dir):
    """
    使用rclone copy从远端下载文件到本地
    参数:
    - remote_path: 远端路径 (如: s3:bucket-name)
    - filename: 文件名
    - local_dir: 本地目录
    返回: (success: bool, local_file_path: str, error_message: str)
    """
    try:
        remote_file_path = f"{remote_path}/{filename}"
        local_file_path = os.path.join(local_dir, filename)

        # 确保本地目录存在
        os.makedirs(local_dir, exist_ok=True)

        cmd = [RCLONE_COMMAND, "copy", remote_file_path, local_dir]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode == 0 and os.path.exists(local_file_path):
            print(f"Successfully copied {remote_file_path} to {local_file_path}")
            return True, local_file_path, None
        else:
            error_msg = f"rclone copy failed: {result.stderr}"
            print(error_msg)
            return False, None, error_msg

    except subprocess.TimeoutExpired:
        error_msg = "rclone copy timeout"
        print(error_msg)
        return False, None, error_msg
    except Exception as e:
        error_msg = f"rclone copy error: {str(e)}"
        print(error_msg)
        return False, None, error_msg

def cleanup_temp_file(file_path, max_age_seconds=3600):
    """
    清理临时文件
    参数:
    - file_path: 文件路径
    - max_age_seconds: 最大保留时间（秒），默认1小时
    """
    try:
        if os.path.exists(file_path):
            file_age = time.time() - os.path.getmtime(file_path)
            if file_age > max_age_seconds:
                os.remove(file_path)
                print(f"Cleaned up temp file: {file_path}")
    except Exception as e:
        print(f"Failed to cleanup temp file {file_path}: {str(e)}")

def download_audio(music_hash, title, artist, album, video_id, partition=0, temp_dir=TEMP_DIR, cookies_file=COOKIES_FILE):
    """
    下载音频文件到临时上传文件夹，然后通过rclone上传到远端
    参数:
    - music_hash: 音乐的MD5哈希值
    - title, artist, album: 音乐元数据
    - video_id: YouTube视频ID
    - partition: 存储分区号
    """
    # 获取临时上传目录
    upload_dir = get_temp_upload_dir()

    # 确保目录存在
    os.makedirs(upload_dir, exist_ok=True)
    os.makedirs(temp_dir, exist_ok=True)

    # 生成YouTube URL
    song_url = f'https://music.youtube.com/watch?v={video_id}'


    # 配置下载选项
    ydl_opts = {
        "extractor_args": {"youtube": {
            "skip": ["translated_subs"],
            'player_client': {'web_music'},
            "po_token":{
                "web_music.gvs+Mlu5L2WA-91KHbkXrS_YjXit2JMfxrm8LIi9kQ4XA1yazXkRNHwIB17hH9nZtRDo-M-mJ-82yPVjjk_Pgw3wJd1m94HfqH4d6K5r28pctk3Ay3sNV3VEXUVnHFDk"
            }
        }},
        'cookiefile': cookies_file,
        'format': '774/251',
        'postprocessors': [{
            'key': 'FFmpegMetadata',
            'add_metadata': True,

        }],
        'paths': {
            'temp': temp_dir,
        },
        'overwrites': True,
        'writethumbnail': True,
        'outtmpl': {
            'default': os.path.join(upload_dir, music_hash + '.%(ext)s')
        }
    }

    # 创建下载器实例并执行下载
    with YoutubeDL(ydl_opts) as ydl:
        try:
            # 先获取可用格式信息
            info = ydl.extract_info(song_url, download=True)
            print(f"选中的音频格式: {info['format']}")

            # 下载完成后，上传文件到远端
            webm_file = os.path.join(upload_dir, f"{music_hash}.webm")
            webp_file = os.path.join(upload_dir, f"{music_hash}.webp")

            remote_path = get_remote_path(partition)

            # 上传音频文件
            if os.path.exists(webm_file):
                success, error = rclone_move_to_remote(webm_file, remote_path, f"{music_hash}.webm")
                if not success:
                    print(f"Failed to upload audio file: {error}")
                    raise Exception(f"Audio upload failed: {error}")

            # 上传图片文件（如果存在）
            if os.path.exists(webp_file):
                success, error = rclone_move_to_remote(webp_file, remote_path, f"{music_hash}.webp")
                if not success:
                    print(f"Failed to upload image file: {error}")
                    # 图片上传失败不阻止整个流程

            # 检查是否有jpg格式的图片
            jpg_file = os.path.join(upload_dir, f"{music_hash}.jpg")
            if os.path.exists(jpg_file):
                success, error = rclone_move_to_remote(jpg_file, remote_path, f"{music_hash}.jpg")
                if not success:
                    print(f"Failed to upload jpg image file: {error}")

            print(f"Successfully processed and uploaded files for {music_hash}")

        except Exception as e:
            print(f"下载或上传出错: {str(e)}")
            # 清理可能残留的本地文件
            for ext in ['webm', 'webp', 'jpg']:
                temp_file = os.path.join(upload_dir, f"{music_hash}.{ext}")
                if os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except:
                        pass
            raise
