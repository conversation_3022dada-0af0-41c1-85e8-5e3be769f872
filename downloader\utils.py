import os
import hashlib

import requests
from yt_dlp import YoutubeDL

from config import BASE_DOWNLOAD_DIR, TEMP_DIR, COOKIES_FILE, BACKEND_URL, DOWNLOAD_KEY, get_partition_dir

def generate_secure_hash_salt(partition, music_hash, format_type):
    """
    生成安全的hash_salt，用于链接验证
    参数: partition(分区), music_hash(音乐hash), format_type(格式)
    返回: 加盐后的hash值
    """
    combined_string = f"{partition}_{music_hash}_{format_type}_{DOWNLOAD_KEY}"
    return hashlib.md5(combined_string.encode('utf-8')).hexdigest()

def download_audio(music_hash, title, artist, album, video_id, partition=0, temp_dir=TEMP_DIR, cookies_file=COOKIES_FILE):
    """
    下载音频文件到指定分区
    参数:
    - music_hash: 音乐的MD5哈希值
    - title, artist, album: 音乐元数据
    - video_id: YouTube视频ID
    - partition: 存储分区号
    """
    # 获取分区目录
    output_dir = get_partition_dir(partition)

    # 确保目录存在
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(temp_dir, exist_ok=True)

    # 生成YouTube URL
    song_url = f'https://music.youtube.com/watch?v={video_id}'


    # 配置下载选项
    ydl_opts = {
        "extractor_args": {"youtube": {
            "skip": ["translated_subs"],
            'player_client': {'web_music'},
            "po_token":{
                "web_music.gvs+Mlu5L2WA-91KHbkXrS_YjXit2JMfxrm8LIi9kQ4XA1yazXkRNHwIB17hH9nZtRDo-M-mJ-82yPVjjk_Pgw3wJd1m94HfqH4d6K5r28pctk3Ay3sNV3VEXUVnHFDk"
            }
        }},
        'cookiefile': cookies_file,
        'format': '774/251',
        'postprocessors': [{
            'key': 'FFmpegMetadata',
            'add_metadata': True,

        }],
        'paths': {
            'temp': temp_dir,
        },
        'overwrites': True,
        'writethumbnail': True,
        'outtmpl': {
            'default': os.path.join(output_dir, music_hash + '.%(ext)s')
        }
    }

    # 创建下载器实例并执行下载
    with YoutubeDL(ydl_opts) as ydl:
        try:
            # 先获取可用格式信息
            info = ydl.extract_info(song_url, download=True)
            print(f"选中的音频格式: {info['format']}")
        except Exception as e:
            print(f"下载出错: {str(e)}")
            raise


# 转码功能已移除 - 现在由前端处理




# JWT、转码和WebDAV功能已移除
# - JWT已被hash_salt验证方式替代
# - 转码现在由前端处理
# - WebDAV将在独立项目中实现