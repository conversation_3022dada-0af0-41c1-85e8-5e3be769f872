'use client';

import { useState } from 'react';
import { Box, Button, Typography, Paper } from '@mui/material';
import { ImageProcessor } from '@/src/app/[locale]/utils/imageProcessor';

const TestImagePage = () => {
    const [originalImage, setOriginalImage] = useState(null);
    const [processedImage, setProcessedImage] = useState(null);
    const [processing, setProcessing] = useState(false);
    const [error, setError] = useState(null);

    const handleFileSelect = (event) => {
        const file = event.target.files[0];
        if (file) {
            setOriginalImage(file);
            setProcessedImage(null);
            setError(null);
        }
    };

    const processImage = async () => {
        if (!originalImage) return;

        setProcessing(true);
        setError(null);

        try {
            // Test the Canvas API image processing
            const processedBlob = await ImageProcessor.cropToSquareJPEG(originalImage, 500, 0.9);
            setProcessedImage(processedBlob);
        } catch (err) {
            setError(err.message);
        } finally {
            setProcessing(false);
        }
    };

    const downloadProcessed = () => {
        if (!processedImage) return;

        const url = URL.createObjectURL(processedImage);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'processed-image.jpg';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    return (
        <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
            <Typography variant="h4" gutterBottom>
                Canvas API Image Processing Test
            </Typography>
            
            <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                    Upload Image
                </Typography>
                <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileSelect}
                    style={{ marginBottom: '16px' }}
                />
                
                {originalImage && (
                    <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                            Selected: {originalImage.name} ({Math.round(originalImage.size / 1024)}KB)
                        </Typography>
                    </Box>
                )}

                <Button 
                    variant="contained" 
                    onClick={processImage}
                    disabled={!originalImage || processing}
                    sx={{ mr: 2 }}
                >
                    {processing ? 'Processing...' : 'Process Image'}
                </Button>

                {processedImage && (
                    <Button 
                        variant="outlined" 
                        onClick={downloadProcessed}
                    >
                        Download Processed
                    </Button>
                )}
            </Paper>

            {error && (
                <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light' }}>
                    <Typography color="error">
                        Error: {error}
                    </Typography>
                </Paper>
            )}

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                {originalImage && (
                    <Paper sx={{ p: 2, flex: 1, minWidth: 300 }}>
                        <Typography variant="h6" gutterBottom>
                            Original Image
                        </Typography>
                        <img 
                            src={URL.createObjectURL(originalImage)}
                            alt="Original"
                            style={{ 
                                maxWidth: '100%', 
                                maxHeight: '300px',
                                objectFit: 'contain'
                            }}
                        />
                    </Paper>
                )}

                {processedImage && (
                    <Paper sx={{ p: 2, flex: 1, minWidth: 300 }}>
                        <Typography variant="h6" gutterBottom>
                            Processed Image (500x500 JPEG)
                        </Typography>
                        <img 
                            src={URL.createObjectURL(processedImage)}
                            alt="Processed"
                            style={{ 
                                maxWidth: '100%', 
                                maxHeight: '300px',
                                objectFit: 'contain'
                            }}
                        />
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                            Size: {Math.round(processedImage.size / 1024)}KB
                        </Typography>
                    </Paper>
                )}
            </Box>

            <Paper sx={{ p: 2, mt: 3, bgcolor: 'info.light' }}>
                <Typography variant="body2">
                    <strong>Test Instructions:</strong><br/>
                    1. Upload any image (PNG, JPEG, WebP, etc.)<br/>
                    2. Click "Process Image" to crop to square and convert to JPEG<br/>
                    3. Compare original vs processed image<br/>
                    4. Download the processed image to verify quality<br/>
                    <br/>
                    This test verifies that Canvas API can replace ffmpeg for image processing.
                </Typography>
            </Paper>
        </Box>
    );
};

export default TestImagePage;
