import os
import requests
import hashlib
from flask import Flask, request, jsonify, send_file, abort
from flask_cors import CORS
from celery import Celery
from utils import download_audio, generate_secure_hash_salt, rclone_copy_from_remote
from config import DOWNLOAD_KEY, BACKEND_URL, get_remote_path, get_temp_download_dir, get_temp_upload_dir

app = Flask(__name__)
CORS(app)

DOWNLOAD_QUEUE = 'download_queue'
# 配置 Celery
config = {}
config['broker_url'] = 'redis://:redis_b5iJDa@1Panel-redis-6Fqj:6379/0'
config['result_backend'] = 'redis://:redis_b5iJDa@1Panel-redis-6Fqj:6379/0'
celery = Celery('app', broker=config['broker_url'])
celery.conf.update(config)
celery.conf.update({
    'task_routes': {
        'download_file': {'queue': DOWNLOAD_QUEUE}
    }
})

# 确保临时目录存在
get_temp_upload_dir()
get_temp_download_dir()

@celery.task(bind=True,queue=DOWNLOAD_QUEUE)
def download_file(self, music_hash, title, artist, album, video_id, partition=0):
    """下载文件的 Celery 任务（优化版）"""
    task_id = f"download_{music_hash}"
    self.request.id = task_id

    try:
        # 使用新的下载函数
        download_audio(
            music_hash=music_hash,
            title=title,
            artist=artist,
            album=album,
            video_id=video_id,
            partition=partition
        )

        # 下载和上传过程在download_audio函数中完成
        # 如果没有异常抛出，说明下载和上传都成功了

        # 通知后端下载完成
        callback_data = {
            "secret": DOWNLOAD_KEY,
            "hash": music_hash
        }
        requests.post(BACKEND_URL + 'update_download_status/', json=callback_data)
        print(f"下载和上传完成: {music_hash}")

        return
    except Exception as exc:
        print(f"下载失败: {str(exc)}")
        raise exc

# WebDAV功能已移除 - 将在独立项目中实现

@celery.task(bind=True, queue=DOWNLOAD_QUEUE)
def cleanup_temp_files_task(self):
    """定期清理临时下载文件"""
    import glob
    import time

    temp_download_dir = get_temp_download_dir()
    temp_upload_dir = get_temp_upload_dir()

    # 清理下载临时文件（超过1小时的）
    for file_pattern in [f"{temp_download_dir}/*", f"{temp_upload_dir}/*"]:
        for file_path in glob.glob(file_pattern):
            if os.path.isfile(file_path):
                try:
                    file_age = time.time() - os.path.getmtime(file_path)
                    if file_age > 3600:  # 1小时
                        os.remove(file_path)
                        print(f"Cleaned up old temp file: {file_path}")
                except Exception as e:
                    print(f"Failed to cleanup {file_path}: {str(e)}")

    print("Temp files cleanup completed")


@app.route('/download', methods=['POST'])
def download():
    """统一的下载接口（优化版）"""
    if request.json.get("secret") != DOWNLOAD_KEY:
        return jsonify({"error": "Unauthorized"}), 401

    # 获取请求参数
    data = request.json
    required_fields = ['music_hash', 'title', 'artist', 'album', 'video_id']

    # 验证必需字段
    for field in required_fields:
        if not data.get(field):
            return jsonify({"error": f"Missing required field: {field}"}), 400

    music_hash = data['music_hash']
    title = data['title']
    artist = data['artist']
    album = data['album']
    video_id = data['video_id']
    partition = data.get('partition', 0)  # 默认分区为0

    # 验证分区号
    if not isinstance(partition, int) or partition < 0:
        return jsonify({"error": "Invalid partition number"}), 400

    # 分区验证（不需要创建本地目录）

    # 提交下载任务到 Celery
    download_file.apply_async(
        (music_hash, title, artist, album, video_id, partition),
        task_id=f"download_{music_hash}",
        queue=DOWNLOAD_QUEUE
    )

    return jsonify({"details": "Download task submitted", "partition": partition}), 200


# 转码接口已移除 - 现在由前端处理转码

# WebDAV上传接口已移除 - 将在独立项目中实现

@app.route('/<int:partition>/<music_hash>/<format_type>/<hash_salt>', methods=['GET'])
def get_file(partition, music_hash, format_type, hash_salt):
    """
    新的统一文件服务接口
    URL格式: /[分区]/[音乐hash]/[格式]/[hash_salt]
    支持格式: webm(音频), webp(图片)
    """
    # 验证hash_salt
    expected_salt = generate_secure_hash_salt(partition, music_hash, format_type)
    if hash_salt != expected_salt:
        abort(403, description="Access denied")

    # 验证格式
    if format_type not in ['webm', 'webp']:
        abort(400, description="Unsupported format")

    # 获取远端路径
    remote_path = get_remote_path(partition)
    temp_download_dir = get_temp_download_dir()

    # 生成文件名
    filename = f"{music_hash}.{format_type}"

    # 尝试从远端下载文件
    success, local_file_path, _ = rclone_copy_from_remote(remote_path, filename, temp_download_dir)

    if not success:
        # 对于图片，尝试jpg格式作为备选
        if format_type == 'webp':
            jpg_filename = f"{music_hash}.jpg"
            success, local_file_path, _ = rclone_copy_from_remote(remote_path, jpg_filename, temp_download_dir)
            if success:
                return send_file(local_file_path, mimetype='image/jpeg', as_attachment=False)

        abort(404, description="File not found")

    # 设置正确的MIME类型
    if format_type == 'webm':
        mimetype = 'audio/webm'
        as_attachment = False  # 音频文件可以在浏览器中播放
    elif format_type == 'webp':
        mimetype = 'image/webp'
        as_attachment = False  # 图片文件可以在浏览器中显示

    return send_file(local_file_path, mimetype=mimetype, as_attachment=as_attachment)

# JWT下载接口已移除 - 现在使用新的hash_salt验证方式

if __name__ == '__main__':
    app.run(debug=True)