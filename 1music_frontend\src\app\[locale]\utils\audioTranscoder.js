import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile, toBlobURL } from '@ffmpeg/util';
import { ImageProcessor } from './imageProcessor';

class AudioTranscoder {
    constructor() {
        this.ffmpeg = null;
        this.isLoaded = false;
    }

    async load() {
        if (this.isLoaded) return;

        this.ffmpeg = new FFmpeg();
        
        // Load FFmpeg with CDN URLs
        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';
        await this.ffmpeg.load({
            coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
            wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
        });

        this.isLoaded = true;
    }

    async cropImageToSquare(imageFile) {
        // Use Canvas API for image processing instead of ffmpeg
        return await ImageProcessor.cropToSquareJPEG(imageFile, 500, 0.9);
    }

    async transcodeAudio(audioFile, coverImageFile, format, metadata = {}) {
        if (!this.isLoaded) await this.load();

        const inputAudioName = 'input_audio';
        const inputImageName = 'input_image.jpg';
        const outputName = `output.${format}`;

        try {
            // Write input audio file
            await this.ffmpeg.writeFile(inputAudioName, await fetchFile(audioFile));

            // Process cover image if provided
            let processedImageBlob = null;
            if (coverImageFile) {
                processedImageBlob = await this.cropImageToSquare(coverImageFile);
                await this.ffmpeg.writeFile(inputImageName, await fetchFile(processedImageBlob));
            }

            // Build FFmpeg command based on format
            let command = ['-i', inputAudioName];
            
            if (coverImageFile) {
                command.push('-i', inputImageName);
                command.push('-map', '0:a', '-map', '1');
            }

            if (format === 'mp3') {
                command.push(
                    '-codec:a', 'libmp3lame',
                    '-b:a', '320k'
                );
                if (coverImageFile) {
                    command.push(
                        '-c:v', 'mjpeg',
                        '-id3v2_version', '3',
                        '-metadata:s:v', 'title=Album cover',
                        '-metadata:s:v', 'comment=Cover (front)',
                        '-metadata:s:v', 'handler_name=Album cover'
                    );
                }
            } else if (format === 'flac') {
                command.push('-codec:a', 'flac');
                if (coverImageFile) {
                    command.push(
                        '-metadata:s:v', 'title=Album cover',
                        '-metadata:s:v', 'comment=Cover (front)',
                        '-disposition:v', 'attached_pic'
                    );
                }
            } else {
                throw new Error(`Unsupported format: ${format}`);
            }

            // Add metadata
            if (metadata.title) command.push('-metadata', `title=${metadata.title}`);
            if (metadata.artist) command.push('-metadata', `artist=${metadata.artist}`);
            if (metadata.album) command.push('-metadata', `album=${metadata.album}`);
            
            // Add custom metadata
            command.push(
                '-metadata', 'PURL=1music.cc',
                '-metadata', 'COMMENT=1music.cc'
            );

            command.push('-y', outputName);

            // Execute transcoding
            await this.ffmpeg.exec(command);

            // Read output file
            const data = await this.ffmpeg.readFile(outputName);
            
            // Clean up
            await this.ffmpeg.deleteFile(inputAudioName);
            if (coverImageFile) {
                await this.ffmpeg.deleteFile(inputImageName);
            }
            await this.ffmpeg.deleteFile(outputName);

            return new Uint8Array(data);

        } catch (error) {
            // Clean up on error
            try {
                await this.ffmpeg.deleteFile(inputAudioName);
                if (coverImageFile) {
                    await this.ffmpeg.deleteFile(inputImageName);
                }
                await this.ffmpeg.deleteFile(outputName);
            } catch (cleanupError) {
                // Ignore cleanup errors
            }
            throw error;
        }
    }

    setProgressCallback(callback) {
        if (this.ffmpeg) {
            this.ffmpeg.on('progress', callback);
        }
    }

    terminate() {
        if (this.ffmpeg) {
            this.ffmpeg.terminate();
            this.isLoaded = false;
        }
    }
}

export default AudioTranscoder;
